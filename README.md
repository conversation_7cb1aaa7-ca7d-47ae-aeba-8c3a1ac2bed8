# Owy Kitchen & Coffee Website

Website untuk Owy Kitchen & Coffee dengan integrasi Instagram feed menggunakan Node.js dan Instagram scraper.

## Fitur

- ✅ Website responsif dengan Bootstrap 5
- ✅ Instagram feed otomatis menggunakan `@aduptive/instagram-scraper`
- ✅ Server Express.js untuk hosting
- ✅ API endpoint untuk Instagram posts
- ✅ Loading states dan error handling
- ✅ Responsive design untuk mobile dan desktop

## Teknologi yang Digunakan

- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript
- **Backend**: Node.js, Express.js
- **Instagram Integration**: @aduptive/instagram-scraper
- **Icons**: Font Awesome 6

## Instalasi

1. Clone atau download project ini
2. Install dependencies:
   ```bash
   npm install
   ```

## Menjalankan Aplikasi

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

Server akan ber<PERSON>lan di `http://localhost:3000`

## API Endpoints

### Instagram Feed
```
GET /api/instagram/:username?limit=12
```

Contoh:
```
GET /api/instagram/owykitchencoffee?limit=6
```

Response:
```json
{
  "success": true,
  "username": "owykitchencoffee",
  "posts": [
    {
      "id": "post_id",
      "shortcode": "ABC123",
      "caption": "Post caption...",
      "mediaUrl": "https://...",
      "thumbnail": "https://...",
      "isVideo": false,
      "timestamp": **********,
      "url": "https://www.instagram.com/p/ABC123/",
      "likes": 100,
      "comments": 10
    }
  ],
  "count": 6
}
```

### Health Check
```
GET /api/health
```

## Konfigurasi Instagram

Untuk mengubah username Instagram yang ditampilkan:

1. Edit file `index.html` pada bagian:
   ```html
   <div id="instagram-feed" data-username="owykitchencoffee">
   ```

2. Atau edit file `assets/js/instagram-feed.js` pada bagian:
   ```javascript
   const username = instagramContainer.dataset.username || 'owykitchencoffee';
   ```

## Struktur Project

```
microstite/
├── assets/
│   ├── css/
│   │   ├── main.min.css
│   │   └── instagram-feed.css
│   ├── images/
│   │   └── logo.png
│   └── js/
│       └── instagram-feed.js
├── index.html
├── server.js
├── package.json
└── README.md
```

## Troubleshooting

### Instagram Feed Tidak Muncul

1. Pastikan server berjalan dengan benar
2. Check console browser untuk error messages
3. Pastikan username Instagram valid dan public
4. Check network tab di browser developer tools

### Server Error

1. Pastikan semua dependencies terinstall: `npm install`
2. Check console untuk error messages
3. Pastikan port 3000 tidak digunakan aplikasi lain

## Deployment

Untuk deploy ke production:

1. Set environment variable `PORT` jika diperlukan
2. Pastikan semua dependencies production terinstall
3. Jalankan dengan `npm start`

## Lisensi

ISC License
