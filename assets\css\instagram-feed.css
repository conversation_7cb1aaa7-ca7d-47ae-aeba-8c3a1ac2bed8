/* Instagram Feed Styles */
.instagram-post {
    margin-bottom: 1rem;
}

.post-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 1;
}

.post-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.post-image-container:hover img {
    transform: scale(1.05);
}

.video-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.post-caption {
    font-size: 0.85rem;
    line-height: 1.3;
}

.post-stats {
    font-size: 0.75rem;
}

.post-stats i {
    margin-right: 2px;
}

/* Loading spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Responsive adjustments for Instagram feed */
@media (max-width: 768px) {
    .instagram-post {
        margin-bottom: 0.75rem;
    }

    .post-caption {
        font-size: 0.8rem;
    }

    .post-stats {
        font-size: 0.7rem;
    }

    /* Mobile: Show 2 columns for Instagram */
    #instagram-feed .row {
        margin: 0 -0.25rem;
    }

    #instagram-feed .col-6 {
        padding: 0 0.25rem;
    }
}

@media (min-width: 769px) {
    /* Desktop: Show 3 columns for Instagram */
    #instagram-feed .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
}

/* Instagram feed container */
#instagram-feed {
    min-height: 200px;
}

/* Error state styling */
.alert-warning {
    border-left: 4px solid #ffc107;
}

/* Hover effects */
.instagram-post a {
    text-decoration: none;
    color: inherit;
}

.instagram-post a:hover {
    text-decoration: none;
    color: inherit;
}

/* Footer styling */
.footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa !important;
}

.designBy {
    font-size: 0.875rem;
}

.designBy a {
    color: #6c757d;
    font-weight: 500;
}

.designBy a:hover {
    color: #495057;
    text-decoration: underline !important;
}

/* Swiper Menu Styling */
.menu-swiper {
    position: relative;
    padding: 0 40px 40px 40px; /* Space for navigation and pagination */
}

.menu-swiper .swiper-slide {
    height: auto;
}

/* Menu styling */
.menu-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 1rem;
    background: #fafafa;
    height: 100%;
    min-height: 280px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.menu-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.menu-category {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    text-align: center;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 0.5rem;
}

/* Grid layout for menu items */
.menu-items-grid {
    display: flex;
    gap: 1rem;
    height: 100%;
}

.menu-column {
    flex: 1;
    min-width: 0; /* Allow flex items to shrink */
}

.menu-items {
    margin-left: 0;
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px dotted #e0e0e0;
}

.menu-item:last-child {
    border-bottom: none;
}

.item-name {
    font-size: 0.9rem;
    color: #555;
    flex: 1;
    word-wrap: break-word;
}

.item-price {
    font-weight: 600;
    color: #d63384;
    font-size: 0.9rem;
    margin-left: 1rem;
    white-space: nowrap;
}

/* Swiper Navigation Buttons */
.menu-swiper .swiper-button-next,
.menu-swiper .swiper-button-prev {
    color: #d63384;
    background: white;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    margin-top: -17.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.menu-swiper .swiper-button-next:hover,
.menu-swiper .swiper-button-prev:hover {
    background: #d63384;
    color: white;
    transform: scale(1.1);
}

.menu-swiper .swiper-button-next::after,
.menu-swiper .swiper-button-prev::after {
    font-size: 14px;
    font-weight: bold;
}

/* Swiper Pagination */
.menu-swiper .swiper-pagination {
    bottom: 10px;
}

.menu-swiper .swiper-pagination-bullet {
    background: #d63384;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.menu-swiper .swiper-pagination-bullet-active {
    opacity: 1;
}

/* Custom colors for icons */
.text-brown {
    color: #8B4513 !important;
}

/* Mobile adjustments */
@media (max-width: 767px) {
    .menu-swiper {
        padding: 0 0 40px 0;
    }

    .menu-swiper .swiper-button-next,
    .menu-swiper .swiper-button-prev {
        display: none; /* Hide navigation buttons on mobile */
    }

    .menu-section {
        min-height: 250px;
    }

    /* Stack columns vertically on mobile if needed */
    .menu-items-grid {
        flex-direction: column;
        gap: 0.5rem;
    }

    .menu-column {
        flex: none;
    }

    .item-name {
        font-size: 0.85rem;
    }

    .item-price {
        font-size: 0.85rem;
    }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 991px) {
    .menu-items-grid {
        gap: 0.75rem;
    }
}

/* Responsive menu */
@media (max-width: 768px) {
    .menu-items {
        margin-left: 1rem;
    }

    .menu-category {
        font-size: 0.95rem;
    }

    .item-name {
        font-size: 0.85rem;
    }

    .item-price {
        font-size: 0.85rem;
    }
}
