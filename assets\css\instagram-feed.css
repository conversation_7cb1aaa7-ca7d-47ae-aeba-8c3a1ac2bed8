/* Instagram Feed Styles */
.instagram-post {
    margin-bottom: 1rem;
}

.post-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 1;
}

.post-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.post-image-container:hover img {
    transform: scale(1.05);
}

.video-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.post-caption {
    font-size: 0.85rem;
    line-height: 1.3;
}

.post-stats {
    font-size: 0.75rem;
}

.post-stats i {
    margin-right: 2px;
}

/* Loading spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Responsive adjustments for Instagram feed */
@media (max-width: 768px) {
    .instagram-post {
        margin-bottom: 0.75rem;
    }

    .post-caption {
        font-size: 0.8rem;
    }

    .post-stats {
        font-size: 0.7rem;
    }

    /* Mobile: Show 2 columns for Instagram */
    #instagram-feed .row {
        margin: 0 -0.25rem;
    }

    #instagram-feed .col-6 {
        padding: 0 0.25rem;
    }
}

@media (min-width: 769px) {

    /* Desktop: Show 3 columns for Instagram */
    #instagram-feed .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
}

/* Instagram feed container */
#instagram-feed {
    min-height: 200px;
}

/* Error state styling */
.alert-warning {
    border-left: 4px solid #ffc107;
}

/* Hover effects */
.instagram-post a {
    text-decoration: none;
    color: inherit;
}

.instagram-post a:hover {
    text-decoration: none;
    color: inherit;
}

/* Swiper Menu Styling */
.menu-swiper {
    position: relative;
    padding: 0 40px 40px 40px;
    /* Space for navigation and pagination */
}

.menu-swiper .swiper-slide {
    height: auto;
    width: auto !important;
    /* Allow slides to have different widths */
    flex-shrink: 0;
}

/* Menu styling */
.menu-section {
    border: none;
    border-radius: 15px;
    padding: 1rem;
    background: #ffffff;
    height: 100%;
    min-height: 280px;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: fit-content;
    min-width: 250px;
    max-width: 450px;
}

.menu-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.menu-category {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    text-align: center;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 0.5rem;
}

/* Grid layout for menu items */
.menu-items-grid {
    display: flex;
    gap: 1rem;
    height: 100%;
    align-items: flex-start;
    width: 100%;
}

.menu-column {
    flex: 1;
    min-width: 120px;
    /* Minimum width for readability */
    display: flex;
    flex-direction: column;
}

/* Specific widths for different menu categories */
.menu-section.coffee-menu {
    min-width: 300px;
    max-width: 380px;
}

.menu-section.noodles-menu {
    min-width: 220px;
    max-width: 280px;
}

.menu-section.ricebowl-menu {
    min-width: 250px;
    max-width: 320px;
}

.menu-section.western-menu {
    min-width: 240px;
    max-width: 300px;
}

.menu-section.noncoffee-menu {
    min-width: 320px;
    max-width: 420px;
}

.menu-section.snack-menu {
    min-width: 350px;
    max-width: 450px;
}

.menu-items {
    margin-left: 0;
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px dotted #e0e0e0;
    min-height: 2.5rem;
    box-sizing: border-box;
}

.menu-item:last-child {
    border-bottom: none;
}

/* Ensure equal spacing between items */
.menu-column .menu-item:not(:last-child) {
    margin-bottom: 0.2rem;
}

/* Better distribution for uneven columns */
.menu-items-grid {
    min-height: 200px;
}

.menu-column {
    justify-content: flex-start;
}

/* Add consistent spacing for all menu items */
.menu-column .menu-item {
    flex-shrink: 0;
}

/* Ensure columns have equal visual weight */
.menu-column:first-child {
    padding-right: 0.5rem;
}

.menu-column:last-child {
    padding-left: 0.5rem;
}

.item-name {
    font-size: 0.9rem;
    color: #555;
    flex: 1;
    word-wrap: break-word;
    line-height: 1.3;
}

.item-price {
    font-weight: 600;
    color: #d63384;
    font-size: 12px;
    margin-left: 1rem;
    white-space: nowrap;
    line-height: 1.3;
}

/* Swiper Navigation Buttons */
.menu-swiper .swiper-button-next,
.menu-swiper .swiper-button-prev {
    color: #d63384;
    background: white;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    margin-top: -17.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.menu-swiper .swiper-button-next:hover,
.menu-swiper .swiper-button-prev:hover {
    background: #d63384;
    color: white;
    transform: scale(1.1);
}

.menu-swiper .swiper-button-next::after,
.menu-swiper .swiper-button-prev::after {
    font-size: 14px;
    font-weight: bold;
}

/* Swiper Pagination */
.menu-swiper .swiper-pagination {
    bottom: 10px;
}

.menu-swiper .swiper-pagination-bullet {
    background: #d63384;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.menu-swiper .swiper-pagination-bullet-active {
    opacity: 1;
}

/* Custom colors for icons */
.text-brown {
    color: #8B4513 !important;
}

/* Mobile adjustments */
@media (max-width: 767px) {
    .menu-swiper {
        padding: 0 0 40px 0;
    }

    .menu-swiper .swiper-button-next,
    .menu-swiper .swiper-button-prev {
        display: none;
        /* Hide navigation buttons on mobile */
    }

    /* Override all specific widths on mobile */
    .menu-section,
    .menu-section.coffee-menu,
    .menu-section.noodles-menu,
    .menu-section.ricebowl-menu,
    .menu-section.western-menu,
    .menu-section.noncoffee-menu,
    .menu-section.snack-menu {
        min-width: 280px;
        max-width: 320px;
        width: 100%;
        min-height: 250px;
    }

    /* Stack columns vertically on mobile if needed */
    .menu-items-grid {
        flex-direction: column;
        gap: 0.5rem;
    }

    .menu-column {
        flex: none;
        min-width: auto;
    }

    .item-name {
        font-size: 0.85rem;
    }

    .item-price {
        font-size: 0.85rem;
    }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 991px) {
    .menu-items-grid {
        gap: 0.75rem;
    }
}

/* Mobile Layout Adjustments */
@media (max-width: 767px) {

    /* Center panel-1 content on mobile */
    .panel-1 {
        text-align: center !important;
        padding: 1rem !important;
        margin-bottom: 2rem;
    }

    .panel-1 .logo {
        margin: 0 auto 1rem auto;
        display: block;
    }

    .panel-1 .title {
        text-align: center;
        margin-bottom: 1rem;
    }

    .panel-1 .description {
        text-align: center;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }

    .panel-1 .social-media {
        text-align: center;
        margin-bottom: 1rem;
    }

    /* Hide designBy from panel-1 on mobile */
    .panel-1 .designBy {
        display: none !important;
    }

    /* Panel-2 normal left alignment on mobile */
    .panel-2 {
        text-align: left !important;
        padding: 1rem !important;
    }

    /* Container padding like normal container */
    .container-fluid {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .row {
        margin-left: -15px !important;
        margin-right: -15px !important;
    }

    .col-md-3,
    .col-md-9 {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
}

/* Footer styling for mobile */
.mobile-footer {
    display: none;
}

@media (max-width: 767px) {
    .container-custom {
        padding: 15px !important;
    }

    .panel-2 {
        margin-left: 0 !important;
    }

    .mobile-footer {
        display: block;
        text-align: center;
        padding: 2rem 1rem 1rem 1rem;
        margin-top: 2rem;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }

    .mobile-footer .designBy {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .mobile-footer .designBy a {
        color: #6c757d;
        font-weight: 500;
        text-decoration: none;
    }

    .mobile-footer .designBy a:hover {
        color: #495057;
        text-decoration: underline;
    }
}

/* Responsive menu */
@media (max-width: 768px) {
    .container-custom {
        padding: 15px !important;
    }

    .panel-2 {
        margin-left: 0 !important;
    }

    .menu-items {
        margin-left: 1rem;
    }

    .menu-category {
        font-size: 0.95rem;
    }

    .item-name {
        font-size: 0.85rem;
    }

    .item-price {
        font-size: 0.85rem;
    }
}