/* Instagram Feed Styles */
.instagram-post {
    margin-bottom: 1rem;
}

.post-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 1;
}

.post-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.post-image-container:hover img {
    transform: scale(1.05);
}

.video-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.post-caption {
    font-size: 0.85rem;
    line-height: 1.3;
}

.post-stats {
    font-size: 0.75rem;
}

.post-stats i {
    margin-right: 2px;
}

/* Loading spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .instagram-post {
        margin-bottom: 0.75rem;
    }
    
    .post-caption {
        font-size: 0.8rem;
    }
    
    .post-stats {
        font-size: 0.7rem;
    }
}

/* Instagram feed container */
#instagram-feed {
    min-height: 200px;
}

/* Error state styling */
.alert-warning {
    border-left: 4px solid #ffc107;
}

/* Hover effects */
.instagram-post a {
    text-decoration: none;
    color: inherit;
}

.instagram-post a:hover {
    text-decoration: none;
    color: inherit;
}

/* Footer styling */
.footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa !important;
}

.designBy {
    font-size: 0.875rem;
}

.designBy a {
    color: #6c757d;
    font-weight: 500;
}

.designBy a:hover {
    color: #495057;
    text-decoration: underline !important;
}

/* Menu styling */
.menu-section {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1rem;
}

.menu-section:last-child {
    border-bottom: none;
    margin-bottom: 0 !important;
}

.menu-category {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.menu-items {
    margin-left: 1.5rem;
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px dotted #e0e0e0;
}

.menu-item:last-child {
    border-bottom: none;
}

.item-name {
    font-size: 0.9rem;
    color: #555;
    flex: 1;
}

.item-price {
    font-weight: 600;
    color: #d63384;
    font-size: 0.9rem;
    margin-left: 1rem;
}

/* Custom colors for icons */
.text-brown {
    color: #8B4513 !important;
}

/* Responsive menu */
@media (max-width: 768px) {
    .menu-items {
        margin-left: 1rem;
    }

    .menu-category {
        font-size: 0.95rem;
    }

    .item-name {
        font-size: 0.85rem;
    }

    .item-price {
        font-size: 0.85rem;
    }
}
