/* Instagram Feed Styles */
.instagram-post {
    margin-bottom: 1rem;
}

.post-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 1;
}

.post-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.post-image-container:hover img {
    transform: scale(1.05);
}

.video-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.post-caption {
    font-size: 0.85rem;
    line-height: 1.3;
}

.post-stats {
    font-size: 0.75rem;
}

.post-stats i {
    margin-right: 2px;
}

/* Loading spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .instagram-post {
        margin-bottom: 0.75rem;
    }
    
    .post-caption {
        font-size: 0.8rem;
    }
    
    .post-stats {
        font-size: 0.7rem;
    }
}

/* Instagram feed container */
#instagram-feed {
    min-height: 200px;
}

/* Error state styling */
.alert-warning {
    border-left: 4px solid #ffc107;
}

/* Hover effects */
.instagram-post a {
    text-decoration: none;
    color: inherit;
}

.instagram-post a:hover {
    text-decoration: none;
    color: inherit;
}
