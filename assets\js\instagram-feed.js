class InstagramFeed {
    constructor(containerId, username, options = {}) {
        this.container = document.getElementById(containerId);
        this.username = username;
        this.options = {
            limit: options.limit || 6,
            showCaption: options.showCaption !== false,
            showLikes: options.showLikes !== false,
            showComments: options.showComments !== false,
            ...options
        };
        this.loading = false;
    }

    async loadFeed() {
        if (this.loading) return;
        
        this.loading = true;
        this.showLoading();

        try {
            const response = await fetch(`/api/instagram/${this.username}?limit=${this.options.limit}`);
            const data = await response.json();

            if (data.success) {
                this.renderPosts(data.posts);
            } else {
                this.showError(data.message || 'Failed to load Instagram feed');
            }
        } catch (error) {
            console.error('Error loading Instagram feed:', error);
            this.showError('Unable to connect to Instagram service');
        } finally {
            this.loading = false;
        }
    }

    showLoading() {
        this.container.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading Instagram posts...</p>
            </div>
        `;
    }

    showError(message) {
        this.container.innerHTML = `
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Instagram Feed Unavailable</strong><br>
                ${message}
            </div>
        `;
    }

    renderPosts(posts) {
        if (!posts || posts.length === 0) {
            this.container.innerHTML = `
                <div class="text-center p-4">
                    <p class="text-muted">No Instagram posts found</p>
                </div>
            `;
            return;
        }

        const postsHtml = posts.map(post => this.renderPost(post)).join('');
        
        this.container.innerHTML = `
            <div class="row g-2">
                ${postsHtml}
            </div>
        `;
    }

    renderPost(post) {
        const caption = post.caption ? this.truncateText(post.caption, 100) : '';
        const likes = post.likes ? this.formatNumber(post.likes) : 0;
        const comments = post.comments ? this.formatNumber(post.comments) : 0;
        
        return `
            <div class="col-6 col-md-4">
                <div class="instagram-post">
                    <a href="${post.url}" target="_blank" rel="noopener noreferrer">
                        <div class="post-image-container">
                            <img src="${post.thumbnail || post.mediaUrl}" 
                                 alt="Instagram post" 
                                 class="img-fluid rounded"
                                 loading="lazy">
                            ${post.isVideo ? '<div class="video-indicator"><i class="fas fa-play"></i></div>' : ''}
                        </div>
                    </a>
                    ${this.options.showCaption && caption ? `
                        <div class="post-caption mt-2">
                            <small class="text-muted">${caption}</small>
                        </div>
                    ` : ''}
                    ${(this.options.showLikes || this.options.showComments) ? `
                        <div class="post-stats mt-1 d-flex gap-3">
                            ${this.options.showLikes ? `<small class="text-muted"><i class="fas fa-heart"></i> ${likes}</small>` : ''}
                            ${this.options.showComments ? `<small class="text-muted"><i class="fas fa-comment"></i> ${comments}</small>` : ''}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    refresh() {
        this.loadFeed();
    }
}

// Auto-initialize Instagram feeds when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Instagram feed for Owy Kitchen & Coffee
    const instagramContainer = document.getElementById('instagram-feed');
    if (instagramContainer) {
        const username = instagramContainer.dataset.username || 'owykitchencoffee';
        const feed = new InstagramFeed('instagram-feed', username, {
            limit: 6,
            showCaption: true,
            showLikes: true,
            showComments: true
        });
        
        feed.loadFeed();
        
        // Optional: Refresh feed every 5 minutes
        setInterval(() => {
            feed.refresh();
        }, 5 * 60 * 1000);
    }
});
