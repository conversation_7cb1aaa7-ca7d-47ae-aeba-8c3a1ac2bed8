class InstagramFeed {
    constructor(containerId, username, options = {}) {
        this.container = document.getElementById(containerId);
        this.username = username;
        this.options = {
            limit: options.limit || 6,
            showCaption: options.showCaption !== false,
            showLikes: options.showLikes !== false,
            showComments: options.showComments !== false,
            ...options
        };
        this.loading = false;
    }

    async loadFeed() {
        if (this.loading) return;
        
        this.loading = true;
        this.showLoading();

        try {
            const response = await fetch(`/api/instagram/${this.username}?limit=${this.options.limit}`);
            const data = await response.json();

            if (data.success) {
                console.log(`✅ Loaded ${data.posts.length} posts from @${this.username}`);
                this.renderPosts(data.posts);
            } else {
                console.log(`❌ Failed to load posts from @${this.username}:`, data.error);
                this.showError(
                    data.message || data.error || 'Failed to load Instagram feed',
                    data.suggestions || []
                );
            }
        } catch (error) {
            console.error('Error loading Instagram feed:', error);
            this.showError('Unable to connect to Instagram service');
        } finally {
            this.loading = false;
        }
    }

    showLoading() {
        this.container.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading Instagram posts...</p>
            </div>
        `;
    }

    showError(message, suggestions = []) {
        const suggestionsHtml = suggestions.length > 0 ? `
            <ul class="mt-2 mb-0">
                ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
            </ul>
        ` : '';

        this.container.innerHTML = `
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Instagram Feed Unavailable</strong><br>
                ${message}
                ${suggestionsHtml}
            </div>
        `;
    }

    renderPosts(posts) {
        console.log('🎨 Rendering posts:', posts);

        if (!posts || posts.length === 0) {
            console.log('❌ No posts to render');
            this.container.innerHTML = `
                <div class="text-center p-4">
                    <p class="text-muted">No Instagram posts found</p>
                </div>
            `;
            return;
        }

        console.log(`✅ Rendering ${posts.length} posts`);
        const postsHtml = posts.map(post => this.renderPost(post)).join('');

        // Responsive grid: 2 columns on mobile, 3 columns on desktop
        this.container.innerHTML = `
            <div class="row g-2">
                ${postsHtml}
            </div>
        `;

        console.log('✅ Posts rendered successfully');
    }

    renderPost(post) {
        const caption = post.caption ? this.truncateText(post.caption, 100) : '';
        const likes = post.likes ? this.formatNumber(post.likes) : 0;
        const comments = post.comments ? this.formatNumber(post.comments) : 0;

        return `
            <div class="col-6 col-md-4">
                <div class="instagram-post">
                    <a href="${post.url}" target="_blank" rel="noopener noreferrer">
                        <div class="post-image-container">
                            <img src='${post.thumbnail || post.mediaUrl}'
                                 alt="Instagram post"
                                 class="img-fluid rounded"
                                 loading="lazy">
                            ${post.isVideo ? '<div class="video-indicator"><i class="fas fa-play"></i></div>' : ''}
                        </div>
                    </a>
                    ${this.options.showCaption && caption ? `
                        <div class="post-caption mt-2">
                            <small class="text-muted">${caption}</small>
                        </div>
                    ` : ''}
                    ${(this.options.showLikes || this.options.showComments) ? `
                        <div class="post-stats mt-1 d-flex gap-3">
                            ${this.options.showLikes ? `<small class="text-muted"><i class="fas fa-heart"></i> ${likes}</small>` : ''}
                            ${this.options.showComments ? `<small class="text-muted"><i class="fas fa-comment"></i> ${comments}</small>` : ''}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    refresh() {
        this.loadFeed();
    }
}

// Auto-initialize Instagram feeds when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM loaded, initializing Instagram feed...');

    // Initialize Instagram feed for Owy Kitchen & Coffee
    const instagramContainer = document.getElementById('instagram-feed');
    console.log('📱 Instagram container:', instagramContainer);

    if (instagramContainer) {
        const username = instagramContainer.dataset.username || 'owykitchenncoffee';
        console.log(`👤 Username: ${username}`);

        const feed = new InstagramFeed('instagram-feed', username, {
            limit: 6,
            showCaption: true,
            showLikes: true,
            showComments: true
        });

        console.log('📸 Loading Instagram feed...');
        feed.loadFeed();

        // Optional: Refresh feed every 5 minutes
        setInterval(() => {
            console.log('🔄 Refreshing Instagram feed...');
            feed.refresh();
        }, 5 * 60 * 1000);
    } else {
        console.error('❌ Instagram container not found!');
    }
});
