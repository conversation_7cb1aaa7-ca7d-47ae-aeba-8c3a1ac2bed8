class MenuSlider {
    constructor() {
        this.slider = document.getElementById('menuSlider');
        this.prevBtn = document.getElementById('menuPrevBtn');
        this.nextBtn = document.getElementById('menuNextBtn');
        this.dotsContainer = document.getElementById('menuDots');
        
        if (!this.slider) return;
        
        this.slides = this.slider.querySelectorAll('.menu-slide');
        this.currentSlide = 0;
        this.slidesPerView = this.getSlidesPerView();
        this.totalSlides = this.slides.length;
        this.maxSlide = Math.max(0, this.totalSlides - this.slidesPerView);
        
        this.init();
    }
    
    getSlidesPerView() {
        return window.innerWidth >= 768 ? 3 : 1;
    }
    
    init() {
        this.createDots();
        this.updateSlider();
        this.bindEvents();
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.slidesPerView = this.getSlidesPerView();
            this.maxSlide = Math.max(0, this.totalSlides - this.slidesPerView);
            this.currentSlide = Math.min(this.currentSlide, this.maxSlide);
            this.updateSlider();
            this.createDots();
        });
    }
    
    createDots() {
        if (!this.dotsContainer) return;
        
        this.dotsContainer.innerHTML = '';
        
        // Only show dots on mobile
        if (window.innerWidth >= 768) {
            this.dotsContainer.style.display = 'none';
            return;
        }
        
        this.dotsContainer.style.display = 'flex';
        
        for (let i = 0; i <= this.maxSlide; i++) {
            const dot = document.createElement('div');
            dot.className = `menu-dot ${i === this.currentSlide ? 'active' : ''}`;
            dot.addEventListener('click', () => this.goToSlide(i));
            this.dotsContainer.appendChild(dot);
        }
    }
    
    bindEvents() {
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prevSlide());
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.nextSlide());
        }
        
        // Touch/swipe support
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        this.slider.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });
        
        this.slider.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
        });
        
        this.slider.addEventListener('touchend', () => {
            if (!isDragging) return;
            isDragging = false;
            
            const diff = startX - currentX;
            const threshold = 50;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    this.nextSlide();
                } else {
                    this.prevSlide();
                }
            }
        });
        
        // Mouse drag support for desktop
        let mouseStartX = 0;
        let mouseCurrentX = 0;
        let isMouseDragging = false;
        
        this.slider.addEventListener('mousedown', (e) => {
            mouseStartX = e.clientX;
            isMouseDragging = true;
            this.slider.style.cursor = 'grabbing';
        });
        
        this.slider.addEventListener('mousemove', (e) => {
            if (!isMouseDragging) return;
            mouseCurrentX = e.clientX;
        });
        
        this.slider.addEventListener('mouseup', () => {
            if (!isMouseDragging) return;
            isMouseDragging = false;
            this.slider.style.cursor = 'grab';
            
            const diff = mouseStartX - mouseCurrentX;
            const threshold = 50;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    this.nextSlide();
                } else {
                    this.prevSlide();
                }
            }
        });
        
        this.slider.addEventListener('mouseleave', () => {
            isMouseDragging = false;
            this.slider.style.cursor = 'grab';
        });
    }
    
    updateSlider() {
        const slideWidth = 100 / this.slidesPerView;
        const translateX = -this.currentSlide * slideWidth;
        
        this.slider.style.transform = `translateX(${translateX}%)`;
        
        // Update navigation buttons
        if (this.prevBtn) {
            this.prevBtn.disabled = this.currentSlide === 0;
        }
        
        if (this.nextBtn) {
            this.nextBtn.disabled = this.currentSlide >= this.maxSlide;
        }
        
        // Update dots
        const dots = this.dotsContainer?.querySelectorAll('.menu-dot');
        if (dots) {
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === this.currentSlide);
            });
        }
    }
    
    nextSlide() {
        if (this.currentSlide < this.maxSlide) {
            this.currentSlide++;
            this.updateSlider();
        }
    }
    
    prevSlide() {
        if (this.currentSlide > 0) {
            this.currentSlide--;
            this.updateSlider();
        }
    }
    
    goToSlide(slideIndex) {
        this.currentSlide = Math.max(0, Math.min(slideIndex, this.maxSlide));
        this.updateSlider();
    }
}

// Initialize menu slider when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎛️ Initializing menu slider...');
    new MenuSlider();
});
