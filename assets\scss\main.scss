@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');

body {
    background-color: #F6EFE1;
    font-family: 'GT Walsheim Trial', sans-serif;
}

.container-custom {
    padding: 0 80px;
}

.panel-1 {
    .logo {
        width: 120px;
    }

    .title {
        font-size: 30px;
        font-weight: 500;
        color: rgb(33, 24, 4);
        margin-top: 20px;
        line-height: 30px;
        font-style: normal;
    }

    .description {
        color: rgb(33, 24, 4);
        font-size: 16px;
        line-height: 21px;
        font-style: normal;
        font-weight: 500;
    }

    .designBy {
        font-size: 16px;
        position: absolute;
        background-color: white;
        padding: 11px 20px;
        border-radius: 20px;
        bottom: 30px;
        color: black;

        a {
            font-weight: bolder;
            color: black;
            text-decoration: none;

        }

        a:hover {
            text-decoration: none;
        }
    }

    .social-media {
        margin-top: 20px;

        a {
            font-size: 24px;
            color: black;
            margin-right: 15px;
            text-decoration: none;
        }

        a:hover {
            text-decoration: none;
        }
    }
}

.panel-2 {
    margin-left: 100px;

    .card {
        border-radius: 35px;
        border: none;
        background-color: #FFF9F0;
    }

    p {
        margin: 0;
    }

    .maps {
        iframe {
            width: 100%;
            height: 350px;
            border-radius: 20px;
        }
    }

    .menus {
        .swiper-wrapper {
            padding-right: 50px;
            padding-left: 50px;
        }

        .menu-items-grid {
            margin-bottom: 20px;
        }
    }

    .swiper-wrapper {
        padding-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .mobile-footer {
        display: block !important;
    }

    .container-custom {
        padding: 15px !important;
    }

    .panel-2 {
        margin-top: -40px;
        margin-left: 0 !important;
    }

    .container-custom {
        padding: 15px !important;
    }

    .panel-2 {
        margin-left: 0 !important;
    }

    .menu-items {
        margin-left: 1rem;
    }

    .menu-category {
        font-size: 0.95rem;
    }

    .item-name {
        font-size: 0.85rem;
    }

    .item-price {
        font-size: 0.85rem;
    }
}

/* Footer styling for mobile */
.mobile-footer {
    display: none;
    text-align: center;
    padding: 1rem 1rem;
    margin-top: -10px;
    border: none;
    background-color: transparent;

    .designBy {
        position: relative;
        margin: auto;
        font-size: 14px;
        background-color: white;
        padding: 11px 20px;
        border-radius: 20px;
        width: 200px;
        bottom: 10px;
        color: black;

        a {
            font-weight: bolder;
            color: black;
            text-decoration: none;

        }

        a:hover {
            text-decoration: none;
        }
    }
}