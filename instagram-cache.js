const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');
const cron = require('node-cron');
const { InstagramScraper } = require('@aduptive/instagram-scraper');

class InstagramCache {
    constructor() {
        this.cacheDir = path.join(__dirname, 'cache');
        this.imagesDir = path.join(this.cacheDir, 'images');
        this.dataFile = path.join(this.cacheDir, 'instagram-data.json');
        this.username = 'owykitchenncoffee';
        this.refreshInterval = 6; // hours
        
        this.init();
    }
    
    async init() {
        // Ensure cache directories exist
        await fs.ensureDir(this.cacheDir);
        await fs.ensureDir(this.imagesDir);
        
        console.log('📦 Instagram Cache Manager initialized');
        console.log(`📁 Cache directory: ${this.cacheDir}`);
        console.log(`🖼️ Images directory: ${this.imagesDir}`);
        
        // Load initial data or fetch if not exists
        await this.loadOrFetchData();
        
        // Setup cron job for every 6 hours
        this.setupCronJob();
    }
    
    async loadOrFetchData() {
        try {
            const cachedData = await this.getCachedData();
            
            if (cachedData && this.isCacheValid(cachedData.timestamp)) {
                console.log('✅ Using cached Instagram data');
                return cachedData;
            } else {
                console.log('🔄 Cache expired or not found, fetching new data...');
                return await this.fetchAndCacheData();
            }
        } catch (error) {
            console.error('❌ Error loading Instagram data:', error.message);
            return await this.getDefaultData();
        }
    }
    
    async getCachedData() {
        try {
            if (await fs.pathExists(this.dataFile)) {
                const data = await fs.readJson(this.dataFile);
                return data;
            }
            return null;
        } catch (error) {
            console.error('❌ Error reading cache file:', error.message);
            return null;
        }
    }
    
    isCacheValid(timestamp) {
        if (!timestamp) return false;
        
        const now = Date.now();
        const cacheAge = now - timestamp;
        const maxAge = this.refreshInterval * 60 * 60 * 1000; // 6 hours in milliseconds
        
        const hoursOld = Math.floor(cacheAge / (60 * 60 * 1000));
        console.log(`📅 Cache age: ${hoursOld} hours (max: ${this.refreshInterval} hours)`);
        
        return cacheAge < maxAge;
    }
    
    async fetchAndCacheData() {
        try {
            console.log(`🔍 Fetching Instagram posts from @${this.username}...`);
            
            // Use same scraper configuration as server.js
            const userAgents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ];
            
            const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
            
            const scraper = new InstagramScraper({
                maxRetries: 5,
                minDelay: 3000,
                maxDelay: 6000,
                timeout: 25000,
                rateLimitPerMinute: 8,
                userAgent: randomUserAgent
            });
            
            const result = await scraper.getPosts(this.username, 4); // Get 4 posts for mobile
            
            if (result.success && result.posts && result.posts.length > 0) {
                console.log(`✅ Successfully fetched ${result.posts.length} posts`);
                
                // Download images and process posts
                const processedPosts = await this.downloadAndProcessPosts(result.posts);
                
                // Save to cache
                const cacheData = {
                    username: this.username,
                    posts: processedPosts,
                    timestamp: Date.now(),
                    count: processedPosts.length,
                    source: 'instagram_scraper',
                    cached_at: new Date().toISOString()
                };
                
                await fs.writeJson(this.dataFile, cacheData, { spaces: 2 });
                console.log(`💾 Cached ${processedPosts.length} posts to ${this.dataFile}`);
                
                return cacheData;
            } else {
                throw new Error('No posts found or scraping failed');
            }
            
        } catch (error) {
            console.error(`❌ Failed to fetch Instagram data: ${error.message}`);
            
            // Try to return existing cache even if expired
            const existingCache = await this.getCachedData();
            if (existingCache) {
                console.log('📦 Using expired cache as fallback');
                return existingCache;
            }
            
            return await this.getDefaultData();
        }
    }
    
    async downloadAndProcessPosts(posts) {
        const processedPosts = [];
        
        for (let i = 0; i < posts.length; i++) {
            const post = posts[i];
            try {
                console.log(`📥 Downloading image ${i + 1}/${posts.length}: ${post.shortcode}`);
                
                const imageUrl = post.display_url;
                const fileName = `${post.shortcode}.jpg`;
                const localPath = path.join(this.imagesDir, fileName);
                
                // Download image
                await this.downloadImage(imageUrl, localPath);
                
                // Create processed post with local image path
                const processedPost = {
                    id: post.id,
                    shortcode: post.shortcode,
                    caption: post.caption || '',
                    mediaUrl: `/cache/images/${fileName}`,
                    thumbnail: `/cache/images/${fileName}`,
                    isVideo: post.is_video || false,
                    timestamp: post.timestamp * 1000, // Convert to milliseconds
                    url: post.url,
                    likes: post.likes || 0,
                    comments: post.comments || 0
                };
                
                processedPosts.push(processedPost);
                console.log(`✅ Processed: ${post.shortcode}`);
                
            } catch (error) {
                console.error(`❌ Error processing post ${post.shortcode}:`, error.message);
            }
        }
        
        return processedPosts;
    }
    
    async downloadImage(url, localPath) {
        try {
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'stream',
                timeout: 30000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            });
            
            const writer = fs.createWriteStream(localPath);
            response.data.pipe(writer);
            
            return new Promise((resolve, reject) => {
                writer.on('finish', resolve);
                writer.on('error', reject);
            });
        } catch (error) {
            throw new Error(`Failed to download image: ${error.message}`);
        }
    }
    
    async getDefaultData() {
        console.log('📋 Using default fallback data');
        return {
            username: this.username,
            posts: [],
            timestamp: Date.now(),
            count: 0,
            source: 'fallback',
            message: 'Instagram feed temporarily unavailable'
        };
    }
    
    setupCronJob() {
        // Run every 6 hours: 0 */6 * * *
        cron.schedule('0 */6 * * *', async () => {
            console.log('⏰ Cron job triggered: Refreshing Instagram cache...');
            await this.fetchAndCacheData();
        });
        
        console.log(`⏰ Cron job scheduled: Every ${this.refreshInterval} hours`);
    }
    
    async getData() {
        const cachedData = await this.getCachedData();
        
        if (cachedData && this.isCacheValid(cachedData.timestamp)) {
            return cachedData;
        } else {
            return await this.fetchAndCacheData();
        }
    }
    
    async forceRefresh() {
        console.log('🔄 Force refreshing Instagram cache...');
        return await this.fetchAndCacheData();
    }
}

module.exports = InstagramCache;
