/**
 * Custom class for Instagram scraping errors
 */
export declare class ScrapeError extends Error {
    readonly code?: string | undefined;
    readonly statusCode?: number | undefined;
    constructor(message: string, code?: string | undefined, statusCode?: number | undefined);
    /**
     * Rate limit exceeded error
     */
    static rateLimited(): ScrapeError;
    /**
     * Profile not found error
     */
    static profileNotFound(username: string): ScrapeError;
    /**
     * Error when parsing Instagram data
     */
    static parseError(): ScrapeError;
    /**
     * Network connection error
     */
    static networkError(details?: string): ScrapeError;
    /**
     * Request timeout error
     */
    static timeout(): ScrapeError;
    /**
     * Access denied to Instagram error
     */
    static accessDenied(): ScrapeError;
    /**
     * Instagram server error
     */
    static serverError(): ScrapeError;
    /**
     * Invalid scraper configuration error
     */
    static invalidConfig(detail: string): ScrapeError;
    /**
     * Checks if an error is an instance of ScrapeError
     */
    static isScrapeError(error: unknown): error is ScrapeError;
    /**
     * Returns an object with error information
     */
    toJSON(): {
        name: string;
        message: string;
        code: string | undefined;
        statusCode: number | undefined;
    };
}
