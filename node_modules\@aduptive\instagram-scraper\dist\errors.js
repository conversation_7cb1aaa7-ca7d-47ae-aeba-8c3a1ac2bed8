"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrapeError = void 0;
/**
 * Custom class for Instagram scraping errors
 */
class ScrapeError extends Error {
    constructor(message, code, statusCode) {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
        this.name = 'ScrapeError';
        // Required for proper functioning with classes extending Error in TypeScript
        Object.setPrototypeOf(this, ScrapeError.prototype);
    }
    /**
     * Rate limit exceeded error
     */
    static rateLimited() {
        return new ScrapeError('Too many requests. Please try again later.', 'RATE_LIMITED', 429);
    }
    /**
     * Profile not found error
     */
    static profileNotFound(username) {
        return new ScrapeError(`Profile '${username}' not found`, 'PROFILE_NOT_FOUND', 404);
    }
    /**
     * Error when parsing Instagram data
     */
    static parseError() {
        return new ScrapeError('Failed to parse Instagram data', 'PARSE_ERROR');
    }
    /**
     * Network connection error
     */
    static networkError(details) {
        return new ScrapeError(`Network error${details ? `: ${details}` : ''}`, 'NETWORK_ERROR');
    }
    /**
     * Request timeout error
     */
    static timeout() {
        return new ScrapeError('Request timed out', 'TIMEOUT', 408);
    }
    /**
     * Access denied to Instagram error
     */
    static accessDenied() {
        return new ScrapeError('Access to Instagram was denied', 'ACCESS_DENIED', 403);
    }
    /**
     * Instagram server error
     */
    static serverError() {
        return new ScrapeError('Instagram server error', 'SERVER_ERROR', 500);
    }
    /**
     * Invalid scraper configuration error
     */
    static invalidConfig(detail) {
        return new ScrapeError(`Invalid configuration: ${detail}`, 'INVALID_CONFIG');
    }
    /**
     * Checks if an error is an instance of ScrapeError
     */
    static isScrapeError(error) {
        return error instanceof ScrapeError;
    }
    /**
     * Returns an object with error information
     */
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            statusCode: this.statusCode,
        };
    }
}
exports.ScrapeError = ScrapeError;
//# sourceMappingURL=errors.js.map