{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,MAAa,WAAY,SAAQ,KAAK;IACpC,YACE,OAAe,EACC,IAAa,EACb,UAAmB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAHC,SAAI,GAAJ,IAAI,CAAS;QACb,eAAU,GAAV,UAAU,CAAS;QAGnC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAE1B,6EAA6E;QAC7E,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,OAAO,IAAI,WAAW,CACpB,4CAA4C,EAC5C,cAAc,EACd,GAAG,CACJ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,QAAgB;QACrC,OAAO,IAAI,WAAW,CACpB,YAAY,QAAQ,aAAa,EACjC,mBAAmB,EACnB,GAAG,CACJ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU;QACf,OAAO,IAAI,WAAW,CAAC,gCAAgC,EAAE,aAAa,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,OAAgB;QAClC,OAAO,IAAI,WAAW,CACpB,gBAAgB,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAC/C,eAAe,CAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,WAAW,CAAC,mBAAmB,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY;QACjB,OAAO,IAAI,WAAW,CACpB,gCAAgC,EAChC,eAAe,EACf,GAAG,CACJ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,OAAO,IAAI,WAAW,CAAC,wBAAwB,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,MAAc;QACjC,OAAO,IAAI,WAAW,CACpB,0BAA0B,MAAM,EAAE,EAClC,gBAAgB,CACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,KAAc;QACjC,OAAO,KAAK,YAAY,WAAW,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;CACF;AAzGD,kCAyGC"}