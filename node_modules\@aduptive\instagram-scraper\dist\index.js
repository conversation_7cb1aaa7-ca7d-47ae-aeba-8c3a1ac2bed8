"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VERSION = exports.InstagramScraper = exports.ScrapeError = void 0;
const scraper_1 = require("./scraper");
var errors_1 = require("./errors");
Object.defineProperty(exports, "ScrapeError", { enumerable: true, get: function () { return errors_1.ScrapeError; } });
var scraper_2 = require("./scraper");
Object.defineProperty(exports, "InstagramScraper", { enumerable: true, get: function () { return scraper_2.InstagramScraper; } });
exports.default = scraper_1.InstagramScraper;
exports.VERSION = '1.0.0';
//# sourceMappingURL=index.js.map