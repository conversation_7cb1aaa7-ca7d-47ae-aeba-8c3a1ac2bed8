import type { ScraperConfig, ScraperResponse } from './types';
export declare class InstagramScraper {
    private readonly axios;
    private readonly config;
    constructor(config?: Partial<ScraperConfig>);
    private getRandomHeaders;
    private delay;
    private fetchPostMedia;
    private fetchFromApi;
    private processPost;
    getPosts(username: string, limit?: number): Promise<ScraperResponse>;
    saveToJson(data: ScraperResponse, filename?: string): Promise<boolean>;
}
