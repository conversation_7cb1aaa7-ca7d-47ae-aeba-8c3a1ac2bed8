"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstagramScraper = void 0;
const axios_1 = __importDefault(require("axios"));
const fs_1 = require("fs");
const constants_1 = require("./constants");
const errors_1 = require("./errors");
class InstagramScraper {
    constructor(config = {}) {
        this.config = { ...constants_1.DEFAULT_CONFIG, ...config };
        this.axios = axios_1.default.create({
            timeout: this.config.timeout,
            headers: this.getRandomHeaders(),
        });
    }
    getRandomHeaders() {
        const userAgent = constants_1.MOBILE_USER_AGENTS[Math.floor(Math.random() * constants_1.MOBILE_USER_AGENTS.length)];
        return {
            'User-Agent': userAgent,
            Accept: '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            Connection: 'keep-alive',
            'X-IG-App-ID': '936619743392459',
            'X-ASBD-ID': '198387',
            'X-IG-WWW-Claim': '0',
            'X-Requested-With': 'XMLHttpRequest',
            Referer: 'https://www.instagram.com/',
            Origin: 'https://www.instagram.com',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
        };
    }
    delay(min = this.config.minDelay, max = this.config.maxDelay) {
        const time = Math.floor(Math.random() * (max - min + 1) + min);
        return new Promise((resolve) => setTimeout(resolve, time));
    }
    async fetchPostMedia(shortcode) {
        var _a, _b, _c, _d;
        try {
            const response = await this.axios.get(`https://www.instagram.com/api/v1/media/${shortcode}/info/`);
            const mediaItems = [];
            const data = response.data;
            if (data.items && data.items[0]) {
                const item = data.items[0];
                if (item.video_versions) {
                    mediaItems.push({
                        url: item.video_versions[0].url,
                        type: 'video',
                        width: item.video_versions[0].width,
                        height: item.video_versions[0].height,
                    });
                    if ((_a = item.image_versions2) === null || _a === void 0 ? void 0 : _a.candidates) {
                        mediaItems.push({
                            url: item.image_versions2.candidates[0].url,
                            type: 'thumbnail',
                            width: item.image_versions2.candidates[0].width,
                            height: item.image_versions2.candidates[0].height,
                        });
                    }
                }
                else if (item.carousel_media) {
                    for (const media of item.carousel_media) {
                        if (media.video_versions) {
                            mediaItems.push({
                                url: media.video_versions[0].url,
                                type: 'video',
                                width: media.video_versions[0].width,
                                height: media.video_versions[0].height,
                            });
                            if ((_b = media.image_versions2) === null || _b === void 0 ? void 0 : _b.candidates) {
                                mediaItems.push({
                                    url: media.image_versions2.candidates[0].url,
                                    type: 'thumbnail',
                                    width: media.image_versions2.candidates[0].width,
                                    height: media.image_versions2.candidates[0].height,
                                });
                            }
                        }
                        else if ((_c = media.image_versions2) === null || _c === void 0 ? void 0 : _c.candidates) {
                            mediaItems.push({
                                url: media.image_versions2.candidates[0].url,
                                type: 'image',
                                width: media.image_versions2.candidates[0].width,
                                height: media.image_versions2.candidates[0].height,
                            });
                        }
                    }
                }
                else if ((_d = item.image_versions2) === null || _d === void 0 ? void 0 : _d.candidates) {
                    mediaItems.push({
                        url: item.image_versions2.candidates[0].url,
                        type: 'image',
                        width: item.image_versions2.candidates[0].width,
                        height: item.image_versions2.candidates[0].height,
                    });
                }
            }
            return mediaItems;
        }
        catch (error) {
            console.error('Error fetching media:', error);
            return [];
        }
    }
    async fetchFromApi(username) {
        try {
            const response = await this.axios.get(`https://www.instagram.com/api/v1/users/web_profile_info/?username=${username}`, {
                headers: {
                    ...this.getRandomHeaders(),
                    'X-IG-App-ID': '936619743392459',
                },
            });
            return response.data;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                // Tratamento específico para cada tipo de erro
                if (error.code === 'ECONNABORTED') {
                    return Promise.reject(errors_1.ScrapeError.timeout());
                }
                if (!error.response) {
                    return Promise.reject(errors_1.ScrapeError.networkError(error.message));
                }
                switch (error.response.status) {
                    case 429:
                        return Promise.reject(errors_1.ScrapeError.rateLimited());
                    case 404:
                        return Promise.reject(errors_1.ScrapeError.profileNotFound(username));
                    case 403:
                        return Promise.reject(errors_1.ScrapeError.accessDenied());
                    default:
                        if (error.response.status >= 500) {
                            return Promise.reject(errors_1.ScrapeError.serverError());
                        }
                        return Promise.reject(errors_1.ScrapeError.networkError(`HTTP Error ${error.response.status}`));
                }
            }
            // Se não for um erro do axios
            return Promise.reject(error instanceof Error
                ? errors_1.ScrapeError.networkError(error.message)
                : errors_1.ScrapeError.networkError('Unknown error'));
        }
    }
    async processPost(post) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        const mediaItems = await this.fetchPostMedia(post.code || post.shortcode);
        let mediaType = 'image';
        if (post.is_video) {
            mediaType = 'video';
        }
        else if (mediaItems.length > 1) {
            mediaType = 'carousel';
        }
        const processedPost = {
            id: post.id,
            shortcode: post.code || post.shortcode,
            timestamp: post.taken_at_timestamp || post.taken_at,
            display_url: post.display_url || ((_c = (_b = (_a = post.image_versions2) === null || _a === void 0 ? void 0 : _a.candidates) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.url),
            caption: ((_g = (_f = (_e = (_d = post.edge_media_to_caption) === null || _d === void 0 ? void 0 : _d.edges) === null || _e === void 0 ? void 0 : _e[0]) === null || _f === void 0 ? void 0 : _f.node) === null || _g === void 0 ? void 0 : _g.text) ||
                ((_h = post.caption) === null || _h === void 0 ? void 0 : _h.text) ||
                '',
            likes: ((_j = post.edge_liked_by) === null || _j === void 0 ? void 0 : _j.count) || post.like_count || 0,
            comments: ((_k = post.edge_media_to_comment) === null || _k === void 0 ? void 0 : _k.count) || post.comment_count || 0,
            is_video: post.is_video,
            url: `https://www.instagram.com/p/${post.code || post.shortcode}/`,
            media_type: mediaType,
            media_items: mediaItems,
        };
        if (mediaType === 'video' && mediaItems.length > 0) {
            const videoItem = mediaItems.find((item) => item.type === 'video');
            const thumbnailItem = mediaItems.find((item) => item.type === 'thumbnail');
            if (videoItem) {
                processedPost.video_url = videoItem.url;
            }
            if (thumbnailItem) {
                processedPost.thumbnail_url = thumbnailItem.url;
            }
        }
        return processedPost;
    }
    async getPosts(username, limit = 20) {
        var _a, _b, _c;
        try {
            if (!username) {
                return {
                    success: false,
                    error: 'Username is required',
                };
            }
            await this.delay();
            const data = await this.fetchFromApi(username);
            if (!((_a = data === null || data === void 0 ? void 0 : data.data) === null || _a === void 0 ? void 0 : _a.user)) {
                throw errors_1.ScrapeError.profileNotFound(username);
            }
            const posts = ((_c = (_b = data.data.user.edge_owner_to_timeline_media) === null || _b === void 0 ? void 0 : _b.edges) === null || _c === void 0 ? void 0 : _c.map((edge) => edge.node)) || [];
            const processedPosts = [];
            for (const post of posts.slice(0, limit)) {
                await this.delay(1000, 2000);
                const processedPost = await this.processPost(post);
                processedPosts.push(processedPost);
            }
            return {
                success: true,
                username,
                posts: processedPosts,
                scraped_at: new Date().toISOString(),
            };
        }
        catch (error) {
            if (error instanceof errors_1.ScrapeError) {
                return {
                    success: false,
                    error: error.message,
                    code: error.code,
                    statusCode: error.statusCode,
                };
            }
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                code: 'UNKNOWN_ERROR',
            };
        }
    }
    async saveToJson(data, filename = 'posts.json') {
        try {
            await fs_1.promises.writeFile(filename, JSON.stringify(data, null, 2), 'utf-8');
            return true;
        }
        catch (error) {
            console.error('Error saving JSON:', error);
            return false;
        }
    }
}
exports.InstagramScraper = InstagramScraper;
//# sourceMappingURL=scraper.js.map