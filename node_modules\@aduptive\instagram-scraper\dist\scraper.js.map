{"version": 3, "file": "scraper.js", "sourceRoot": "", "sources": ["../src/scraper.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,2BAAoC;AACpC,2CAAiE;AACjE,qCAAuC;AAQvC,MAAa,gBAAgB;IAI3B,YAAY,SAAiC,EAAE;QAC7C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,0BAAc,EAAE,GAAG,MAAM,EAAE,CAAC;QAC/C,IAAI,CAAC,KAAK,GAAG,eAAK,CAAC,MAAM,CAAC;YACxB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE;SACjC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB;QACtB,MAAM,SAAS,GACb,8BAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,8BAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;QAE5E,OAAO;YACL,YAAY,EAAE,SAAS;YACvB,MAAM,EAAE,KAAK;YACb,iBAAiB,EAAE,gBAAgB;YACnC,iBAAiB,EAAE,mBAAmB;YACtC,UAAU,EAAE,YAAY;YACxB,aAAa,EAAE,iBAAiB;YAChC,WAAW,EAAE,QAAQ;YACrB,gBAAgB,EAAE,GAAG;YACrB,kBAAkB,EAAE,gBAAgB;YACpC,OAAO,EAAE,4BAA4B;YACrC,MAAM,EAAE,2BAA2B;YACnC,gBAAgB,EAAE,aAAa;YAC/B,gBAAgB,EAAE,MAAM;YACxB,gBAAgB,EAAE,OAAO;SAC1B,CAAC;IACJ,CAAC;IAEO,KAAK,CACX,MAAc,IAAI,CAAC,MAAM,CAAC,QAAQ,EAClC,MAAc,IAAI,CAAC,MAAM,CAAC,QAAQ;QAElC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAC/D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,SAAiB;;QAC5C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CACnC,0CAA0C,SAAS,QAAQ,CAC5D,CAAC;YAEF,MAAM,UAAU,GAAgB,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE3B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE3B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxB,UAAU,CAAC,IAAI,CAAC;wBACd,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG;wBAC/B,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK;wBACnC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM;qBACtC,CAAC,CAAC;oBACH,IAAI,MAAA,IAAI,CAAC,eAAe,0CAAE,UAAU,EAAE,CAAC;wBACrC,UAAU,CAAC,IAAI,CAAC;4BACd,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG;4BAC3C,IAAI,EAAE,WAAW;4BACjB,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK;4BAC/C,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM;yBAClD,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;qBAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC/B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACxC,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;4BACzB,UAAU,CAAC,IAAI,CAAC;gCACd,GAAG,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG;gCAChC,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK;gCACpC,MAAM,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM;6BACvC,CAAC,CAAC;4BACH,IAAI,MAAA,KAAK,CAAC,eAAe,0CAAE,UAAU,EAAE,CAAC;gCACtC,UAAU,CAAC,IAAI,CAAC;oCACd,GAAG,EAAE,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG;oCAC5C,IAAI,EAAE,WAAW;oCACjB,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK;oCAChD,MAAM,EAAE,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM;iCACnD,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;6BAAM,IAAI,MAAA,KAAK,CAAC,eAAe,0CAAE,UAAU,EAAE,CAAC;4BAC7C,UAAU,CAAC,IAAI,CAAC;gCACd,GAAG,EAAE,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG;gCAC5C,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK;gCAChD,MAAM,EAAE,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM;6BACnD,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;qBAAM,IAAI,MAAA,IAAI,CAAC,eAAe,0CAAE,UAAU,EAAE,CAAC;oBAC5C,UAAU,CAAC,IAAI,CAAC;wBACd,GAAG,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG;wBAC3C,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK;wBAC/C,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM;qBAClD,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CACnC,qEAAqE,QAAQ,EAAE,EAC/E;gBACE,OAAO,EAAE;oBACP,GAAG,IAAI,CAAC,gBAAgB,EAAE;oBAC1B,aAAa,EAAE,iBAAiB;iBACjC;aACF,CACF,CAAC;YAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,+CAA+C;gBAC/C,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBAClC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAW,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC/C,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACpB,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAW,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBACjE,CAAC;gBAED,QAAQ,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBAC9B,KAAK,GAAG;wBACN,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAW,CAAC,WAAW,EAAE,CAAC,CAAC;oBACnD,KAAK,GAAG;wBACN,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;oBAC/D,KAAK,GAAG;wBACN,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAW,CAAC,YAAY,EAAE,CAAC,CAAC;oBACpD;wBACE,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;4BACjC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAW,CAAC,WAAW,EAAE,CAAC,CAAC;wBACnD,CAAC;wBACD,OAAO,OAAO,CAAC,MAAM,CACnB,oBAAW,CAAC,YAAY,CAAC,cAAc,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAChE,CAAC;gBACN,CAAC;YACH,CAAC;YAED,8BAA8B;YAC9B,OAAO,OAAO,CAAC,MAAM,CACnB,KAAK,YAAY,KAAK;gBACpB,CAAC,CAAC,oBAAW,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;gBACzC,CAAC,CAAC,oBAAW,CAAC,YAAY,CAAC,eAAe,CAAC,CAC9C,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAS;;QACjC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1E,IAAI,SAAS,GAAmC,OAAO,CAAC;QACxD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,SAAS,GAAG,OAAO,CAAC;QACtB,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,SAAS,GAAG,UAAU,CAAC;QACzB,CAAC;QAED,MAAM,aAAa,GAAkB;YACnC,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS;YACtC,SAAS,EAAE,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ;YACnD,WAAW,EACT,IAAI,CAAC,WAAW,KAAI,MAAA,MAAA,MAAA,IAAI,CAAC,eAAe,0CAAE,UAAU,0CAAG,CAAC,CAAC,0CAAE,GAAG,CAAA;YAChE,OAAO,EACL,CAAA,MAAA,MAAA,MAAA,MAAA,IAAI,CAAC,qBAAqB,0CAAE,KAAK,0CAAG,CAAC,CAAC,0CAAE,IAAI,0CAAE,IAAI;iBAClD,MAAA,IAAI,CAAC,OAAO,0CAAE,IAAI,CAAA;gBAClB,EAAE;YACJ,KAAK,EAAE,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,KAAK,KAAI,IAAI,CAAC,UAAU,IAAI,CAAC;YACxD,QAAQ,EAAE,CAAA,MAAA,IAAI,CAAC,qBAAqB,0CAAE,KAAK,KAAI,IAAI,CAAC,aAAa,IAAI,CAAC;YACtE,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,GAAG,EAAE,+BAA+B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG;YAClE,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,UAAU;SACxB,CAAC;QAEF,IAAI,SAAS,KAAK,OAAO,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;YACnE,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CACnC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CACpC,CAAC;YAEF,IAAI,SAAS,EAAE,CAAC;gBACd,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC;YAC1C,CAAC;YACD,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,CAAC,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC;YAClD,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEM,KAAK,CAAC,QAAQ,CACnB,QAAgB,EAChB,QAAgB,EAAE;;QAElB,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,sBAAsB;iBAC9B,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YAEnB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE/C,IAAI,CAAC,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,IAAI,CAAA,EAAE,CAAC;gBACtB,MAAM,oBAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,KAAK,GACT,CAAA,MAAA,MAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,0CAAE,KAAK,0CAAE,GAAG,CACrD,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CACzB,KAAI,EAAE,CAAC;YAEV,MAAM,cAAc,GAAoB,EAAE,CAAC;YAE3C,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC7B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACnD,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrC,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ;gBACR,KAAK,EAAE,cAAc;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,oBAAW,EAAE,CAAC;gBACjC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,UAAU,EAAE,KAAK,CAAC,UAAU;iBAC7B,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EACH,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;gBACnE,IAAI,EAAE,eAAe;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,UAAU,CACrB,IAAqB,EACrB,WAAmB,YAAY;QAE/B,IAAI,CAAC;YACH,MAAM,aAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACrE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AArRD,4CAqRC"}