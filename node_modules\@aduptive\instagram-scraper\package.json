{"name": "@aduptive/instagram-scraper", "version": "1.0.3", "description": "Modern TypeScript library for collecting public Instagram content with smart delays, mobile-first approach, and media support", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "LICENSE", "README.md"], "scripts": {"debug": "ts-node debug.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "lint": "eslint . --ext .ts", "prepare": "npm run clean && npm run build", "prepublishOnly": "npm test && npm run lint", "preversion": "npm run lint", "version": "npm run format && git add -A src", "postversion": "git push && git push --tags", "format": "prettier --write \"src/**/*.ts\""}, "keywords": ["instagram", "scraper", "typescript", "social-media", "data-collection", "web-scraping", "public-content", "no-authentication", "rate-limiting", "mobile-first", "media", "video", "carousel"], "author": "aduptive", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/aduptive/instagram-scraper.git"}, "bugs": {"url": "https://github.com/aduptive/instagram-scraper/issues"}, "homepage": "https://github.com/aduptive/instagram-scraper#readme", "dependencies": {"axios": "^1.6.7", "cheerio": "^1.0.0-rc.12"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/jest": "^29.5.14", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint": "^8.56.0", "jest": "^29.7.0", "prettier": "^3.2.1", "rimraf": "^5.0.5", "ts-jest": "^29.2.5", "typescript": "^5.3.3"}, "engines": {"node": ">=14.0.0"}, "publishConfig": {"access": "public"}}