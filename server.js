const express = require('express');
const path = require('path');
const cors = require('cors');
const { InstagramScraper } = require('@aduptive/instagram-scraper');
const InstagramCache = require('./instagram-cache');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Instagram Cache
const instagramCache = new InstagramCache();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Serve static files (CSS, images, etc.)
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Serve cached Instagram images
app.use('/cache', express.static(path.join(__dirname, 'cache')));

// Serve main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Test Instagram username endpoint
app.get('/api/test-username/:username', async (req, res) => {
    try {
        const { username } = req.params;
        console.log(`Testing Instagram username: ${username}`);

        // Rotate user agents to avoid detection
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        ];

        const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
        console.log(`🎭 Using User-Agent: ${randomUserAgent.substring(0, 50)}...`);

        const scraper = new InstagramScraper({
                maxRetries: 3,
                minDelay: 2000,
                maxDelay: 5000,
                timeout: 15000,
                rateLimitPerMinute: 10,
                userAgent: randomUserAgent
            });

        const result = await scraper.getPosts(username, 1);

        res.json({
            username: username,
            exists: result.success,
            error: result.error || null,
            code: result.code || null
        });

    } catch (error) {
        res.json({
            username: req.params.username,
            exists: false,
            error: error.message,
            code: 'SCRAPER_ERROR'
        });
    }
});

// Instagram API endpoint with caching
app.get('/api/instagram/:username', async (req, res) => {
    try {
        const { username } = req.params;
        const limit = req.query.limit || 6; // Default to 6 posts

        console.log(`📱 Instagram API request for: ${username} (limit: ${limit})`);

        // Only serve owykitchenncoffee from cache, others use old method
        if (username === 'owykitchenncoffee') {
            console.log('📦 Using Instagram cache for owykitchenncoffee');
            const cachedData = await instagramCache.getData();

            if (cachedData && cachedData.posts && cachedData.posts.length > 0) {
                // Limit posts based on request
                const limitedPosts = cachedData.posts.slice(0, parseInt(limit));

                const response = {
                    success: true,
                    username: username,
                    posts: limitedPosts,
                    count: limitedPosts.length,
                    source: 'cache',
                    cached_at: cachedData.cached_at,
                    cache_age_hours: Math.floor((Date.now() - cachedData.timestamp) / (60 * 60 * 1000))
                };

                console.log(`✅ Served ${limitedPosts.length} cached posts for @${username}`);
                return res.json(response);
            } else {
                console.log('❌ No cached data available, trying live fetch...');
                // Fall through to live fetch
            }
        }

        console.log(`🔍 Live fetching Instagram posts for: ${username}`);

        // Try to get real Instagram data from owykitchencoffee
        try {
            console.log(`Attempting to fetch Instagram posts from: ${username}`);
            // Use same user agent rotation for main scraper
            const userAgents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ];

            const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

            const scraper = new InstagramScraper({
                maxRetries: 5,
                minDelay: 3000,
                maxDelay: 6000,
                timeout: 25000,
                rateLimitPerMinute: 8,
                userAgent: randomUserAgent
            });

            const result = await scraper.getPosts(username, parseInt(limit));
            console.log('Instagram scraper result:', result);

            if (result.success && result.posts && result.posts.length > 0) {
                console.log(`✅ Successfully fetched ${result.posts.length} real Instagram posts from @${username}`);

                // Format the response to match our frontend expectations with proxy URLs
                const formattedPosts = result.posts.map(post => {
                    const originalImageUrl = post.display_url;
                    const originalThumbnailUrl = post.thumbnail_url || post.display_url;

                    return {
                        id: post.id,
                        shortcode: post.shortcode,
                        caption: post.caption || '',
                        mediaUrl: `/api/proxy-image?url=${encodeURIComponent(originalImageUrl)}`,
                        thumbnail: `/api/proxy-image?url=${encodeURIComponent(originalThumbnailUrl)}`,
                        originalMediaUrl: originalImageUrl,
                        originalThumbnail: originalThumbnailUrl,
                        isVideo: post.is_video,
                        timestamp: post.timestamp,
                        url: post.url,
                        likes: post.likes || 0,
                        comments: post.comments || 0
                    };
                });

                return res.json({
                    success: true,
                    username: username,
                    posts: formattedPosts,
                    count: formattedPosts.length,
                    source: 'instagram_direct',
                    message: `Real posts from @${username}`
                });
            } else {
                console.log(`❌ Instagram scraper failed for @${username}:`, result.error);
            }
        } catch (scraperError) {
            console.log(`❌ Instagram scraper error for @${username}:`, scraperError.message);
        }

        // If direct scraping fails, return error
        console.log(`❌ Unable to fetch posts from @${username}. This could be because:`);
        console.log('   - The account is private');
        console.log('   - The account does not exist');
        console.log('   - Instagram is blocking the request');
        console.log('   - Network connectivity issues');

        // Return error response
        res.status(404).json({
            success: false,
            error: `Unable to fetch posts from @${username}`,
            message: `The Instagram account @${username} could not be accessed. Please check if the account exists and is public.`,
            suggestions: [
                'Verify the Instagram username is correct',
                'Make sure the account is public (not private)',
                'Check if the account exists on Instagram',
                'Try again later as Instagram may be temporarily blocking requests'
            ],
            username: username
        });

    } catch (error) {
        console.error('Error in Instagram endpoint:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch Instagram posts',
            message: error.message
        });
    }
});

// Image proxy endpoint to bypass CORS
app.get('/api/proxy-image', async (req, res) => {
    try {
        const imageUrl = req.query.url;

        if (!imageUrl) {
            return res.status(400).json({ error: 'URL parameter is required' });
        }

        console.log(`🖼️ Proxying image: ${imageUrl}`);

        const axios = require('axios');
        const response = await axios.get(imageUrl, {
            responseType: 'stream',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.instagram.com/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
            },
            timeout: 10000
        });

        // Set appropriate headers
        res.set({
            'Content-Type': response.headers['content-type'] || 'image/jpeg',
            'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
            'Access-Control-Allow-Origin': '*'
        });

        // Pipe the image data
        response.data.pipe(res);

    } catch (error) {
        console.error('❌ Error proxying image:', error.message);
        res.status(500).json({ error: 'Failed to proxy image' });
    }
});

// Force refresh Instagram cache
app.post('/api/instagram/refresh', async (req, res) => {
    try {
        console.log('🔄 Manual cache refresh requested');
        const refreshedData = await instagramCache.forceRefresh();

        res.json({
            success: true,
            message: 'Instagram cache refreshed successfully',
            data: refreshedData,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Error refreshing cache:', error.message);
        res.status(500).json({
            success: false,
            error: 'Failed to refresh Instagram cache',
            message: error.message
        });
    }
});

// Get cache status
app.get('/api/instagram/cache-status', async (req, res) => {
    try {
        const cachedData = await instagramCache.getCachedData();

        if (cachedData) {
            const cacheAge = Date.now() - cachedData.timestamp;
            const hoursOld = Math.floor(cacheAge / (60 * 60 * 1000));
            const isValid = instagramCache.isCacheValid(cachedData.timestamp);

            res.json({
                success: true,
                cache_exists: true,
                cache_valid: isValid,
                cache_age_hours: hoursOld,
                posts_count: cachedData.posts ? cachedData.posts.length : 0,
                cached_at: cachedData.cached_at,
                next_refresh: new Date(cachedData.timestamp + (6 * 60 * 60 * 1000)).toISOString()
            });
        } else {
            res.json({
                success: true,
                cache_exists: false,
                message: 'No cache data found'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Failed to get cache status',
            message: error.message
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        service: 'Owy Kitchen & Coffee Server with Instagram Cache'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
    console.log(`📱 Instagram API available at http://localhost:${PORT}/api/instagram/[username]`);
});

module.exports = app;
