const express = require('express');
const path = require('path');
const cors = require('cors');
const { InstagramScraper } = require('@aduptive/instagram-scraper');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Serve static files (CSS, images, etc.)
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Serve main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Test Instagram username endpoint
app.get('/api/test-username/:username', async (req, res) => {
    try {
        const { username } = req.params;
        console.log(`Testing Instagram username: ${username}`);

        const scraper = new InstagramScraper({
            maxRetries: 2,
            minDelay: 1000,
            maxDelay: 2000,
            timeout: 10000
        });

        const result = await scraper.getPosts(username, 1);

        res.json({
            username: username,
            exists: result.success,
            error: result.error || null,
            code: result.code || null
        });

    } catch (error) {
        res.json({
            username: req.params.username,
            exists: false,
            error: error.message,
            code: 'SCRAPER_ERROR'
        });
    }
});

// Instagram API endpoint
app.get('/api/instagram/:username', async (req, res) => {
    try {
        const { username } = req.params;
        const limit = req.query.limit || 6; // Default to 6 posts

        console.log(`Fetching Instagram posts for: ${username}`);

        // Try to get real Instagram data from owykitchencoffee
        try {
            console.log(`Attempting to fetch Instagram posts from: ${username}`);
            const scraper = new InstagramScraper({
                maxRetries: 5,
                minDelay: 1000,
                maxDelay: 3000,
                timeout: 20000
            });

            const result = await scraper.getPosts(username, parseInt(limit));
            console.log('Instagram scraper result:', result);

            if (result.success && result.posts && result.posts.length > 0) {
                console.log(`✅ Successfully fetched ${result.posts.length} real Instagram posts from @${username}`);

                // Format the response to match our frontend expectations with proxy URLs
                const formattedPosts = result.posts.map(post => {
                    const originalImageUrl = post.display_url;
                    const originalThumbnailUrl = post.thumbnail_url || post.display_url;

                    return {
                        id: post.id,
                        shortcode: post.shortcode,
                        caption: post.caption || '',
                        mediaUrl: `/api/proxy-image?url=${encodeURIComponent(originalImageUrl)}`,
                        thumbnail: `/api/proxy-image?url=${encodeURIComponent(originalThumbnailUrl)}`,
                        originalMediaUrl: originalImageUrl,
                        originalThumbnail: originalThumbnailUrl,
                        isVideo: post.is_video,
                        timestamp: post.timestamp,
                        url: post.url,
                        likes: post.likes || 0,
                        comments: post.comments || 0
                    };
                });

                return res.json({
                    success: true,
                    username: username,
                    posts: formattedPosts,
                    count: formattedPosts.length,
                    source: 'instagram_direct',
                    message: `Real posts from @${username}`
                });
            } else {
                console.log(`❌ Instagram scraper failed for @${username}:`, result.error);
            }
        } catch (scraperError) {
            console.log(`❌ Instagram scraper error for @${username}:`, scraperError.message);
        }

        // If direct scraping fails, return error
        console.log(`❌ Unable to fetch posts from @${username}. This could be because:`);
        console.log('   - The account is private');
        console.log('   - The account does not exist');
        console.log('   - Instagram is blocking the request');
        console.log('   - Network connectivity issues');

        // Return error response
        res.status(404).json({
            success: false,
            error: `Unable to fetch posts from @${username}`,
            message: `The Instagram account @${username} could not be accessed. Please check if the account exists and is public.`,
            suggestions: [
                'Verify the Instagram username is correct',
                'Make sure the account is public (not private)',
                'Check if the account exists on Instagram',
                'Try again later as Instagram may be temporarily blocking requests'
            ],
            username: username
        });

    } catch (error) {
        console.error('Error in Instagram endpoint:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch Instagram posts',
            message: error.message
        });
    }
});

// Image proxy endpoint to bypass CORS
app.get('/api/proxy-image', async (req, res) => {
    try {
        const imageUrl = req.query.url;

        if (!imageUrl) {
            return res.status(400).json({ error: 'URL parameter is required' });
        }

        console.log(`🖼️ Proxying image: ${imageUrl}`);

        const axios = require('axios');
        const response = await axios.get(imageUrl, {
            responseType: 'stream',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.instagram.com/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
            },
            timeout: 10000
        });

        // Set appropriate headers
        res.set({
            'Content-Type': response.headers['content-type'] || 'image/jpeg',
            'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
            'Access-Control-Allow-Origin': '*'
        });

        // Pipe the image data
        response.data.pipe(res);

    } catch (error) {
        console.error('❌ Error proxying image:', error.message);
        res.status(500).json({ error: 'Failed to proxy image' });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        service: 'Owy Kitchen & Coffee Server'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
    console.log(`📱 Instagram API available at http://localhost:${PORT}/api/instagram/[username]`);
});

module.exports = app;
