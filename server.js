const express = require('express');
const path = require('path');
const cors = require('cors');
const { InstagramScraper } = require('@aduptive/instagram-scraper');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Serve static files (CSS, images, etc.)
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Serve main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Instagram API endpoint
app.get('/api/instagram/:username', async (req, res) => {
    try {
        const { username } = req.params;
        const limit = req.query.limit || 12; // Default to 12 posts
        
        console.log(`Fetching Instagram posts for: ${username}`);
        
        // Create Instagram scraper instance
        const scraper = new InstagramScraper();
        
        // Fetch user posts
        const posts = await scraper.getUserPosts(username, { limit: parseInt(limit) });
        
        // Format the response
        const formattedPosts = posts.map(post => ({
            id: post.id,
            shortcode: post.shortcode,
            caption: post.caption || '',
            mediaUrl: post.displayUrl,
            thumbnail: post.thumbnailSrc,
            isVideo: post.isVideo,
            timestamp: post.takenAtTimestamp,
            url: `https://www.instagram.com/p/${post.shortcode}/`,
            likes: post.edgeMediaPreviewLike?.count || 0,
            comments: post.edgeMediaToComment?.count || 0
        }));
        
        res.json({
            success: true,
            username: username,
            posts: formattedPosts,
            count: formattedPosts.length
        });
        
    } catch (error) {
        console.error('Error fetching Instagram posts:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch Instagram posts',
            message: error.message
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        service: 'Owy Kitchen & Coffee Server'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
    console.log(`📱 Instagram API available at http://localhost:${PORT}/api/instagram/[username]`);
});

module.exports = app;
